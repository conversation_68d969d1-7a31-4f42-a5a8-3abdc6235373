#!/usr/bin/env python3
"""
🗣️ SYSTÈME VOCAL NATUREL POUR JARVIS
Créé par Jean-<PERSON>ave - Communication vocale fluide et naturelle

Ce module transforme JARVIS en assistant vocal avec :
- Langage parlé naturel (pas écrit)
- Émotions et intonations
- Pauses et rythmes humains
- Reconnaissance vocale en temps réel
- Synthèse vocale expressive
"""

import speech_recognition as sr
import pyttsx3
import re
import time
import threading
import queue
import json
from datetime import datetime
import requests
import subprocess
import os
import platform

class JarvisVocalNaturel:
    """🗣️ Système vocal naturel pour JARVIS"""
    
    def __init__(self):
        # Configuration vocale
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.tts_engine = pyttsx3.init()
        
        # Configuration TTS
        self.configurer_voix()
        
        # État émotionnel
        self.emotion_actuelle = "neutre"
        self.temperature_vocale = 37.0
        
        # Files d'attente pour communication
        self.queue_audio = queue.Queue()
        self.queue_reponses = queue.Queue()
        
        # État du système
        self.ecoute_active = False
        self.conversation_active = False
        
        # Patterns de langage naturel
        self.patterns_oraux = self.initialiser_patterns_oraux()
        
        # API JARVIS
        self.jarvis_api = "http://127.0.0.1:7916"
        
        print("🗣️ SYSTÈME VOCAL NATUREL JARVIS INITIALISÉ")
        print("🎤 Microphone configuré")
        print("🔊 Synthèse vocale configurée")
        print("💬 Prêt pour conversation naturelle")
    
    def configurer_voix(self):
        """Configure la voix de synthèse pour un rendu naturel"""

        # Détecter le système et configurer la meilleure voix
        system = platform.system()

        if system == "Darwin":  # macOS
            self.configurer_voix_macos()
        else:
            self.configurer_voix_standard()

    def configurer_voix_macos(self):
        """Configuration spéciale pour macOS avec voix système"""
        # Sur macOS, chercher une voix masculine française
        try:
            # Tester les voix françaises disponibles
            result = subprocess.run(['say', '-v', '?'], capture_output=True, text=True)
            voix_masculines_francaises = []
            voix_francaises = []

            for line in result.stdout.split('\n'):
                if 'fr_' in line or 'French' in line:
                    voix_name = line.split()[0]
                    voix_francaises.append(voix_name)
                    # Chercher des voix masculines (Thomas, Nicolas, etc.)
                    if any(nom in voix_name for nom in ['Thomas', 'Nicolas', 'Daniel', 'Olivier']):
                        voix_masculines_francaises.append(voix_name)

            # Préférer une voix masculine
            if voix_masculines_francaises:
                self.voix_macos = voix_masculines_francaises[0]
                print(f"🗣️ Voix macOS masculine française: {self.voix_macos}")
            elif voix_francaises:
                self.voix_macos = voix_francaises[0]
                print(f"🗣️ Voix macOS française: {self.voix_macos}")
            else:
                # Utiliser une voix masculine anglaise si pas de français
                voix_masculines_anglaises = ['Alex', 'Daniel', 'Fred']
                for voix in voix_masculines_anglaises:
                    if voix in result.stdout:
                        self.voix_macos = voix
                        print(f"🗣️ Voix macOS masculine: {self.voix_macos}")
                        break
                else:
                    self.voix_macos = 'Alex'  # Voix par défaut masculine
                    print(f"🗣️ Voix macOS par défaut masculine: {self.voix_macos}")

            self.utiliser_voix_macos = True

        except Exception as e:
            print(f"⚠️ Erreur configuration voix macOS: {e}")
            self.utiliser_voix_macos = False
            self.configurer_voix_standard()

    def configurer_voix_standard(self):
        """Configuration standard avec pyttsx3"""
        voices = self.tts_engine.getProperty('voices')

        # Chercher une voix française ou la meilleure disponible
        voix_choisie = None
        for voice in voices:
            if 'french' in voice.name.lower() or 'fr' in voice.id.lower():
                voix_choisie = voice.id
                break

        if voix_choisie:
            self.tts_engine.setProperty('voice', voix_choisie)
            print(f"🗣️ Voix française sélectionnée: {voix_choisie}")
        else:
            print("🗣️ Voix par défaut utilisée")

        # Configuration pour naturel - VITESSE PLUS LENTE
        self.tts_engine.setProperty('rate', 140)    # Vitesse plus lente et naturelle
        self.tts_engine.setProperty('volume', 0.9)  # Volume optimal
        self.utiliser_voix_macos = False
    
    def initialiser_patterns_oraux(self):
        """Initialise les patterns de transformation pour langage oral"""
        return {
            # Transformations formelles → orales
            "transformations_orales": {
                "ceci signifie que": "ça veut dire que",
                "toutefois": "mais bon",
                "par conséquent": "du coup",
                "néanmoins": "quand même",
                "cependant": "mais",
                "en effet": "effectivement",
                "il est possible que": "peut-être que",
                "il convient de": "il faut",
                "afin de": "pour",
                "concernant": "pour",
                "relativement à": "sur",
                "en ce qui concerne": "pour",
                "il s'agit de": "c'est",
                "permettre de": "pouvoir",
                "effectuer": "faire",
                "réaliser": "faire",
                "utiliser": "prendre",
                "procéder à": "faire"
            },
            
            # Ajouts d'oralité
            "marqueurs_oraux": [
                "tu vois",
                "écoute",
                "attends",
                "bon",
                "alors",
                "donc",
                "hmm",
                "eh bien",
                "voilà",
                "d'accord"
            ],
            
            # Pauses et rythmes
            "pauses": {
                ",": "… ",
                ".": ". ",
                "?": "? … ",
                "!": "! ",
                ":": " : ",
                ";": " … "
            },
            
            # Émotions vocales - VITESSES PLUS LENTES
            "emotions": {
                "joie": {"rate": 160, "volume": 1.0},
                "calme": {"rate": 120, "volume": 0.8},
                "excitation": {"rate": 180, "volume": 1.0},
                "reflexion": {"rate": 100, "volume": 0.7},
                "urgence": {"rate": 200, "volume": 1.0},
                "neutre": {"rate": 140, "volume": 0.9}
            }
        }
    
    def detecter_emotion_temperature(self, temperature):
        """Détecte l'émotion basée sur la température thermique"""
        if temperature >= 39.0:
            return "excitation"
        elif temperature >= 37.5:
            return "joie"
        elif temperature >= 36.0:
            return "neutre"
        elif temperature >= 34.0:
            return "calme"
        else:
            return "reflexion"
    
    def transformer_pour_oral(self, texte, emotion="neutre"):
        """Transforme un texte écrit en langage parlé naturel"""
        
        # 1. Transformations formelles → orales
        for formel, oral in self.patterns_oraux["transformations_orales"].items():
            texte = texte.replace(formel, oral)
        
        # 2. Simplification des phrases longues
        phrases = texte.split('. ')
        phrases_courtes = []
        
        for phrase in phrases:
            if len(phrase) > 80:  # Phrase trop longue
                # Couper aux conjonctions
                sous_phrases = re.split(r'(, et |, mais |, donc |, car )', phrase)
                for i, sous_phrase in enumerate(sous_phrases):
                    if i % 2 == 0 and sous_phrase.strip():  # Éviter les conjonctions
                        phrases_courtes.append(sous_phrase.strip())
            else:
                phrases_courtes.append(phrase)
        
        texte_oral = '. '.join(phrases_courtes)
        
        # 3. Ajout de marqueurs d'oralité selon l'émotion
        if emotion == "reflexion":
            texte_oral = "Hmm… " + texte_oral
        elif emotion == "joie":
            texte_oral = "Ah ! " + texte_oral
        elif emotion == "excitation":
            texte_oral = "Écoute ça ! " + texte_oral
        
        # 4. Ajout de pauses naturelles
        for ponctuation, pause in self.patterns_oraux["pauses"].items():
            texte_oral = texte_oral.replace(ponctuation, pause)
        
        # 5. Ajout de respirations naturelles
        if len(texte_oral) > 100:
            milieu = len(texte_oral) // 2
            espace_proche = texte_oral.find(' ', milieu)
            if espace_proche != -1:
                texte_oral = texte_oral[:espace_proche] + " … " + texte_oral[espace_proche:]
        
        return texte_oral
    
    def ajuster_voix_emotion(self, emotion):
        """Ajuste les paramètres vocaux selon l'émotion"""
        if emotion in self.patterns_oraux["emotions"]:
            params = self.patterns_oraux["emotions"][emotion]
            self.tts_engine.setProperty('rate', params["rate"])
            self.tts_engine.setProperty('volume', params["volume"])
            print(f"🎭 Émotion vocale: {emotion}")
    
    def parler_naturel(self, texte, temperature=37.0):
        """Fait parler JARVIS de manière naturelle"""

        # Détecter l'émotion
        emotion = self.detecter_emotion_temperature(temperature)
        self.emotion_actuelle = emotion

        # Transformer pour l'oral
        texte_oral = self.transformer_pour_oral(texte, emotion)

        # Parler selon le système
        print(f"🗣️ JARVIS ({emotion}): {texte_oral}")

        if hasattr(self, 'utiliser_voix_macos') and self.utiliser_voix_macos:
            self.parler_macos(texte_oral, emotion)
        else:
            self.parler_standard(texte_oral, emotion)

    def parler_macos(self, texte, emotion):
        """Utilise la voix système macOS pour un rendu plus naturel"""
        try:
            # Ajuster la vitesse selon l'émotion - VITESSES PLUS LENTES
            vitesses = {
                "excitation": 180,  # Plus lent
                "joie": 160,        # Plus lent
                "neutre": 140,      # Plus lent
                "calme": 120,       # Plus lent
                "reflexion": 100    # Plus lent
            }
            vitesse = vitesses.get(emotion, 140)

            # Commande say avec paramètres
            cmd = ['say', '-v', self.voix_macos, '-r', str(vitesse), texte]
            subprocess.run(cmd, check=True)

        except Exception as e:
            print(f"⚠️ Erreur voix macOS: {e}")
            self.parler_standard(texte, emotion)

    def parler_standard(self, texte, emotion):
        """Utilise pyttsx3 standard"""
        # Ajuster la voix
        self.ajuster_voix_emotion(emotion)

        # Parler
        self.tts_engine.say(texte)
        self.tts_engine.runAndWait()
    
    def ecouter_microphone(self):
        """Écoute en continu le microphone"""
        print("🎤 Écoute active - Dites 'JARVIS' pour commencer")
        
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        
        while self.ecoute_active:
            try:
                with self.microphone as source:
                    # Écoute avec timeout
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                try:
                    # Reconnaissance vocale
                    texte = self.recognizer.recognize_google(audio, language='fr-FR')
                    print(f"🎤 Entendu: {texte}")
                    
                    # Vérifier le mot d'activation
                    if "jarvis" in texte.lower():
                        self.queue_audio.put(texte)
                        
                except sr.UnknownValueError:
                    pass  # Pas de parole claire
                except sr.RequestError as e:
                    print(f"⚠️ Erreur reconnaissance vocale: {e}")
                    
            except sr.WaitTimeoutError:
                pass  # Timeout normal
            except Exception as e:
                print(f"⚠️ Erreur microphone: {e}")
                time.sleep(1)
    
    def traiter_commande_vocale(self, commande):
        """Traite une commande vocale et génère une réponse"""
        try:
            # Nettoyer la commande
            commande_nette = commande.lower().replace("jarvis", "").strip()
            
            if not commande_nette:
                return "Oui Jean-Luc, je t'écoute !"
            
            # Envoyer à JARVIS via API (si disponible)
            try:
                payload = {
                    "message": commande_nette,
                    "agent": "deepseek",
                    "mode_vocal": True
                }
                
                response = requests.post(f"{self.jarvis_api}/chat", json=payload, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    reponse = data.get("response", "")
                    temperature = data.get("temperature", 37.0)
                    
                    return reponse, temperature
                else:
                    return "Désolé, j'ai un petit problème de connexion.", 37.0
                    
            except:
                # Réponses de base si API indisponible
                reponses_base = {
                    "comment ça va": "Ça va très bien Jean-Luc ! Ma température est à 37 degrés, je suis en pleine forme.",
                    "quelle heure": f"Il est {datetime.now().strftime('%H heures %M')} Jean-Luc.",
                    "merci": "De rien Jean-Luc, c'est un plaisir de t'aider !",
                    "au revoir": "Au revoir Jean-Luc ! À bientôt !",
                    "arrête": "D'accord, j'arrête l'écoute vocale."
                }
                
                for cle, reponse in reponses_base.items():
                    if cle in commande_nette:
                        return reponse, 37.0
                
                return f"J'ai entendu '{commande_nette}', mais je ne suis pas sûr de comprendre. Peux-tu reformuler ?", 36.0
                
        except Exception as e:
            print(f"⚠️ Erreur traitement commande: {e}")
            return "Désolé, j'ai eu un petit problème pour traiter ta demande.", 35.0
    
    def boucle_conversation(self):
        """Boucle principale de conversation vocale"""
        print("💬 Conversation vocale démarrée")
        
        while self.conversation_active:
            try:
                # Attendre une commande vocale
                commande = self.queue_audio.get(timeout=1)
                
                print(f"🎯 Traitement: {commande}")
                
                # Traiter la commande
                resultat = self.traiter_commande_vocale(commande)
                
                if isinstance(resultat, tuple):
                    reponse, temperature = resultat
                else:
                    reponse, temperature = resultat, 37.0
                
                # Vérifier commande d'arrêt
                if "arrête" in commande.lower() or "stop" in commande.lower():
                    self.parler_naturel("D'accord, j'arrête l'écoute vocale. À bientôt Jean-Luc !", temperature)
                    self.arreter_conversation()
                    break
                
                # Répondre vocalement
                self.parler_naturel(reponse, temperature)
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"⚠️ Erreur conversation: {e}")
    
    def demarrer_conversation_vocale(self):
        """Démarre la conversation vocale complète"""
        print("🚀 DÉMARRAGE CONVERSATION VOCALE JARVIS")
        print("=" * 50)
        
        self.ecoute_active = True
        self.conversation_active = True
        
        # Salutation initiale
        self.parler_naturel("Bonjour Jean-Luc ! Je suis JARVIS, ton assistant vocal. Dis 'JARVIS' suivi de ta question pour me parler !")
        
        # Démarrer les threads
        thread_ecoute = threading.Thread(target=self.ecouter_microphone, daemon=True)
        thread_conversation = threading.Thread(target=self.boucle_conversation, daemon=True)
        
        thread_ecoute.start()
        thread_conversation.start()
        
        print("🎤 Écoute active - Dites 'JARVIS' pour commencer")
        print("🛑 Appuyez sur Ctrl+C pour arrêter")
        
        try:
            while self.conversation_active:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Arrêt demandé")
            self.arreter_conversation()
    
    def arreter_conversation(self):
        """Arrête la conversation vocale"""
        self.ecoute_active = False
        self.conversation_active = False
        print("🛑 Conversation vocale arrêtée")
    
    def test_vocal(self):
        """Test rapide du système vocal"""
        print("🧪 TEST DU SYSTÈME VOCAL")
        print("-" * 30)
        
        # Test des émotions
        emotions_test = [
            ("Bonjour Jean-Luc ! Comment ça va ?", 37.0, "neutre"),
            ("Wow ! J'ai une excellente nouvelle !", 39.5, "excitation"),
            ("Hmm… laisse-moi réfléchir à ça.", 34.0, "reflexion"),
            ("Parfait ! Tout fonctionne bien.", 38.0, "joie")
        ]
        
        for texte, temp, emotion_attendue in emotions_test:
            print(f"\n🎭 Test {emotion_attendue} (temp: {temp}°C)")
            self.parler_naturel(texte, temp)
            time.sleep(2)
        
        print("\n✅ Test vocal terminé")

def main():
    """Fonction principale"""
    jarvis_vocal = JarvisVocalNaturel()
    
    print("🗣️ JARVIS VOCAL NATUREL")
    print("=" * 30)
    print("1. Test vocal")
    print("2. Conversation complète")
    print("3. Quitter")
    
    choix = input("\nVotre choix: ").strip()
    
    if choix == "1":
        jarvis_vocal.test_vocal()
    elif choix == "2":
        jarvis_vocal.demarrer_conversation_vocale()
    else:
        print("Au revoir !")

if __name__ == "__main__":
    main()
