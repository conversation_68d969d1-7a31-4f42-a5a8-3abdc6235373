#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ DÉMARRAGE RAPIDE JARVIS ANTI-DÉCONNEXION
Jean-Luc <PERSON>ave - Lancement immédiat avec protection

Ce script lance JARVIS immédiatement avec le système anti-déconnexion
sur localhost:7915 avec toutes les protections activées.
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# Configuration
JARVIS_DIR = "/Volumes/seagate/Louna_Electron_Latest/JARVIS_COMPLET_STABLE"
JARVIS_SCRIPT = "jarvis_verrouille.py"
JARVIS_PORT = 7915

def afficher_demarrage():
    """Affiche l'écran de démarrage"""
    print("⚡" * 80)
    print("⚡ DÉMARRAGE RAPIDE JARVIS ANTI-DÉCONNEXION")
    print("⚡" * 80)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"👤 Jean-Luc Passave")
    print(f"🌐 Interface: http://localhost:{JARVIS_PORT}")
    print("🛡️ Protection anti-déconnexion: ACTIVÉE")
    print("⚡" * 80)

def verifier_environnement():
    """Vérification rapide de l'environnement"""
    print("\n🔍 Vérification rapide...")
    
    # Vérifier le répertoire
    if not os.path.exists(JARVIS_DIR):
        print(f"❌ Répertoire JARVIS introuvable: {JARVIS_DIR}")
        return False
    
    # Vérifier le script
    jarvis_path = os.path.join(JARVIS_DIR, JARVIS_SCRIPT)
    if not os.path.exists(jarvis_path):
        print(f"❌ Script JARVIS introuvable: {jarvis_path}")
        return False
    
    print("✅ Environnement OK")
    return True

def lancer_jarvis_rapide():
    """Lance JARVIS rapidement"""
    print("\n🚀 LANCEMENT JARVIS...")
    print("-" * 50)
    
    try:
        # Changer vers le répertoire JARVIS
        os.chdir(JARVIS_DIR)
        
        # Afficher les informations de démarrage
        print(f"📁 Répertoire: {os.getcwd()}")
        print(f"🐍 Script: {JARVIS_SCRIPT}")
        print(f"🌐 Port: {JARVIS_PORT}")
        print("-" * 50)
        
        print("🛡️ SYSTÈME ANTI-DÉCONNEXION ACTIVÉ")
        print("✅ Monitoring continu 24/7")
        print("✅ Reconnexion automatique")
        print("✅ Sauvegarde temps réel")
        print("✅ Circuit breaker intelligent")
        print("✅ Récupération après panne")
        print("-" * 50)
        
        print("💡 COMMANDES DISPONIBLES:")
        print("   • 'statut superviseur' - État du système")
        print("   • Ctrl+C - Arrêt propre")
        print("-" * 50)
        
        print(f"🌐 Interface sera disponible sur: http://localhost:{JARVIS_PORT}")
        print("⏳ Démarrage en cours...")
        print("=" * 80)
        
        # Lancer JARVIS
        cmd = ["python3", JARVIS_SCRIPT]
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")
        print("✅ JARVIS arrêté proprement")
        
    except Exception as e:
        print(f"\n❌ Erreur lancement: {e}")
        return False
    
    return True

def main():
    """Fonction principale"""
    afficher_demarrage()
    
    # Vérification rapide
    if not verifier_environnement():
        print("\n❌ ÉCHEC VÉRIFICATION - ARRÊT")
        sys.exit(1)
    
    # Message de confirmation
    print("\n🎯 PRÊT À DÉMARRER JARVIS AVEC PROTECTION ANTI-DÉCONNEXION")
    print("🛡️ Votre JARVIS sera protégé contre toutes les déconnexions")
    
    # Démarrage immédiat ou avec confirmation
    if len(sys.argv) > 1 and sys.argv[1] == "--immediat":
        print("\n⚡ DÉMARRAGE IMMÉDIAT...")
        time.sleep(1)
    else:
        input("\n⏸️ Appuyez sur Entrée pour démarrer JARVIS...")
    
    # Lancer JARVIS
    if lancer_jarvis_rapide():
        print("\n✅ JARVIS s'est arrêté normalement")
    else:
        print("\n❌ JARVIS s'est arrêté avec des erreurs")
        sys.exit(1)

if __name__ == "__main__":
    main()
