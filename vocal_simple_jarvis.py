#!/usr/bin/env python3
"""
🗣️ MODULE VOCAL SIMPLE POUR JARVIS
Module vocal qui s'intègre parfaitement avec jarvis_verrouille.py
"""

import subprocess
import platform
import threading
import time

class VocalSimpleJarvis:
    """🗣️ Système vocal simple et efficace"""
    
    def __init__(self):
        self.system = platform.system()
        self.voix_masculine = None
        self.initialiser_voix()
        
    def initialiser_voix(self):
        """Initialise la voix masculine"""
        if self.system == "Darwin":  # macOS
            try:
                # Chercher une voix masculine française
                result = subprocess.run(['say', '-v', '?'], capture_output=True, text=True)
                
                # Voix masculines préférées
                voix_preferees = ['Thomas', '<PERSON>', '<PERSON>', '<PERSON>']
                
                for voix in voix_preferees:
                    if voix in result.stdout:
                        self.voix_masculine = voix
                        print(f"🗣️ Voix masculine sélectionnée: {voix}")
                        return
                
                # Fallback
                self.voix_masculine = 'Alex'
                print(f"🗣️ Voix par défaut: {self.voix_masculine}")
                
            except Exception as e:
                print(f"⚠️ Erreur voix: {e}")
                self.voix_masculine = None
        else:
            print("🗣️ Système non-macOS - Vocal désactivé")
            self.voix_masculine = None
    
    def parler(self, texte, vitesse=140):
        """Fait parler JARVIS avec voix masculine"""
        if not self.voix_masculine:
            print(f"🗣️ JARVIS: {texte}")
            return
        
        try:
            # Transformer pour l'oral
            texte_oral = self.transformer_oral(texte)
            
            # Parler avec voix masculine
            cmd = ['say', '-v', self.voix_masculine, '-r', str(vitesse), texte_oral]
            subprocess.run(cmd, check=False)
            
        except Exception as e:
            print(f"⚠️ Erreur vocal: {e}")
            print(f"🗣️ JARVIS: {texte}")
    
    def parler_async(self, texte, vitesse=140):
        """Parle en arrière-plan"""
        thread = threading.Thread(target=self.parler, args=(texte, vitesse), daemon=True)
        thread.start()
    
    def transformer_oral(self, texte):
        """Transforme le texte pour l'oral"""
        # Transformations simples
        transformations = {
            "ceci signifie que": "ça veut dire que",
            "toutefois": "mais bon",
            "par conséquent": "du coup",
            "néanmoins": "quand même",
            "cependant": "mais",
            "en effet": "effectivement",
            "il est possible que": "peut-être que",
            "afin de": "pour",
            "concernant": "pour"
        }
        
        for formel, oral in transformations.items():
            texte = texte.replace(formel, oral)
        
        return texte
    
    def parler_emotion(self, texte, emotion="neutre"):
        """Parle avec émotion"""
        vitesses = {
            "excitation": 180,
            "joie": 160,
            "neutre": 140,
            "calme": 120,
            "reflexion": 100
        }
        
        vitesse = vitesses.get(emotion, 140)
        
        # Ajouter des marqueurs selon l'émotion
        if emotion == "excitation":
            texte = f"Écoute ça ! {texte}"
        elif emotion == "reflexion":
            texte = f"Hmm… {texte}"
        elif emotion == "joie":
            texte = f"Ah ! {texte}"
        
        self.parler_async(texte, vitesse)

# Instance globale
vocal_jarvis = VocalSimpleJarvis()
