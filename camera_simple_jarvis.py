#!/usr/bin/env python3
"""
📹 MODULE CAMÉRA SIMPLE POUR JARVIS
Module caméra qui s'intègre parfaitement avec jarvis_verrouille.py
"""

try:
    import cv2
    import numpy as np
    CAMERA_DISPONIBLE = True
except ImportError:
    print("⚠️ OpenCV non installé - Caméra désactivée")
    CAMERA_DISPONIBLE = False
    cv2 = None
    np = None

import time
import threading
from datetime import datetime

class CameraSimpleJarvis:
    """📹 Système caméra simple et efficace"""
    
    def __init__(self):
        self.camera = None
        self.camera_active = False
        self.derniere_image = None
        self.thread_capture = None
        self.running = False
        
        if CAMERA_DISPONIBLE:
            self.initialiser_camera()
    
    def initialiser_camera(self):
        """Initialise la caméra"""
        try:
            self.camera = cv2.VideoCapture(0)
            if self.camera.isOpened():
                self.camera_active = True
                print("📹 Caméra initialisée")
                self.demarrer_capture()
            else:
                print("⚠️ Caméra non disponible")
        except Exception as e:
            print(f"⚠️ Erreur caméra: {e}")
    
    def demarrer_capture(self):
        """Démarre la capture en arrière-plan"""
        if not self.camera_active:
            return
        
        self.running = True
        self.thread_capture = threading.Thread(target=self.boucle_capture, daemon=True)
        self.thread_capture.start()
    
    def boucle_capture(self):
        """Boucle de capture d'images"""
        while self.running and self.camera_active:
            try:
                ret, frame = self.camera.read()
                if ret:
                    self.derniere_image = frame
                time.sleep(0.1)  # 10 FPS
            except Exception as e:
                print(f"⚠️ Erreur capture: {e}")
                break
    
    def obtenir_image(self):
        """Obtient la dernière image capturée"""
        return self.derniere_image
    
    def analyser_image(self):
        """Analyse l'image actuelle"""
        if self.derniere_image is None:
            return "Aucune image disponible"
        
        try:
            height, width = self.derniere_image.shape[:2]
            
            # Analyse basique
            couleur_moyenne = np.mean(self.derniere_image, axis=(0, 1))
            luminosite = np.mean(cv2.cvtColor(self.derniere_image, cv2.COLOR_BGR2GRAY))
            
            # Détection de mouvement simple
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            analyse = f"""📹 ANALYSE VISUELLE:
📏 Résolution: {width}x{height}
🌈 Couleurs: B{couleur_moyenne[0]:.0f} G{couleur_moyenne[1]:.0f} R{couleur_moyenne[2]:.0f}
💡 Luminosité: {luminosite:.0f}/255
⏰ Capture: {timestamp}
👤 Présence détectée: {'Oui' if luminosite > 50 else 'Faible éclairage'}"""
            
            return analyse
            
        except Exception as e:
            return f"Erreur analyse: {e}"
    
    def detecter_presence(self):
        """Détecte une présence"""
        if self.derniere_image is None:
            return False
        
        try:
            gray = cv2.cvtColor(self.derniere_image, cv2.COLOR_BGR2GRAY)
            luminosite = np.mean(gray)
            
            # Simple détection basée sur la luminosité
            return luminosite > 30
            
        except Exception:
            return False
    
    def obtenir_statut(self):
        """Obtient le statut de la caméra"""
        if not CAMERA_DISPONIBLE:
            return "❌ OpenCV non installé"
        elif not self.camera_active:
            return "❌ Caméra non disponible"
        elif self.derniere_image is not None:
            return "✅ Caméra active"
        else:
            return "⏳ Initialisation..."
    
    def arreter(self):
        """Arrête la caméra"""
        self.running = False
        if self.camera:
            self.camera.release()
        print("📹 Caméra arrêtée")

# Instance globale
camera_jarvis = CameraSimpleJarvis()
