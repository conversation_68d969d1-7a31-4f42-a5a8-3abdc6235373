#!/usr/bin/env python3
"""
🚀 DÉMARRAGE SIMPLE JARVIS POUR JEAN-LUC
Fichier unique pour démarrer JARVIS avec toutes les protections

UTILISATION SIMPLE :
python3 DEMARRER_JARVIS.py

C'est tout ! Le système fait le reste automatiquement.
"""

import os
import sys

# Aller dans le bon dossier
os.chdir("/Volumes/seagate/Louna_Electron_Latest/JARVIS_COMPLET_STABLE")

print("🚀 DÉMARRAGE JARVIS AVEC PROTECTION COMPLÈTE")
print("=" * 50)
print("✅ Sauvegarde automatique toutes les 10 minutes")
print("✅ Vérification d'intégrité continue")
print("✅ Rollback automatique si problème")
print("✅ Gestion automatique des dépendances")
print("=" * 50)
print()

# Démarrer le système de protection complète
os.system("python3 jarvis_protection_complete.py demarrer")
