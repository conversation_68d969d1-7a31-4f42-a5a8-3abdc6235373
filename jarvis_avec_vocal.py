#!/usr/bin/env python3
"""
🗣️ JARVIS COMPLET AVEC SYSTÈME VOCAL NATUREL
Intégration complète du système vocal avec JARVIS

Ce script lance JARVIS avec toutes ses capacités + communication vocale naturelle
"""

import os
import sys
import time
import threading
import subprocess
from datetime import datetime

# Ajouter le dossier courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from jarvis_vocal_naturel import JarvisVocalNaturel
except ImportError as e:
    print(f"❌ Erreur import vocal: {e}")
    print("💡 Installez d'abord les dépendances avec: python3 installer_vocal_jarvis.py")
    sys.exit(1)

class JarvisCompletAvecVocal:
    """🗣️ JARVIS complet avec système vocal intégré"""
    
    def __init__(self):
        self.jarvis_process = None
        self.vocal_system = None
        self.running = False
        
        print("🚀 JARVIS COMPLET AVEC SYSTÈME VOCAL")
        print("=" * 50)
        print("🧠 Intelligence artificielle avancée")
        print("🗣️ Communication vocale naturelle")
        print("🔥 Mémoire thermique active")
        print("⚡ Performance optimisée")
        print("=" * 50)
    
    def demarrer_jarvis_principal(self):
        """Démarre le système JARVIS principal"""
        print("🧠 Démarrage du système JARVIS principal...")
        
        try:
            # Démarrer JARVIS en arrière-plan
            self.jarvis_process = subprocess.Popen(
                [sys.executable, "jarvis_verrouille.py"],
                cwd=os.path.dirname(os.path.abspath(__file__)),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Attendre que JARVIS soit prêt
            print("⏳ Attente du démarrage de JARVIS...")
            time.sleep(15)  # Laisser le temps à JARVIS de démarrer
            
            # Vérifier que JARVIS fonctionne
            if self.jarvis_process.poll() is None:
                print("✅ JARVIS principal démarré avec succès")
                return True
            else:
                print("❌ Échec démarrage JARVIS principal")
                return False
                
        except Exception as e:
            print(f"❌ Erreur démarrage JARVIS: {e}")
            return False
    
    def demarrer_systeme_vocal(self):
        """Démarre le système vocal"""
        print("🗣️ Initialisation du système vocal...")
        
        try:
            self.vocal_system = JarvisVocalNaturel()
            print("✅ Système vocal initialisé")
            return True
        except Exception as e:
            print(f"❌ Erreur système vocal: {e}")
            return False
    
    def demarrer_complet(self):
        """Démarre JARVIS complet avec vocal"""
        print("\n🚀 DÉMARRAGE COMPLET DE JARVIS AVEC VOCAL")
        print("=" * 60)
        
        # 1. Démarrer JARVIS principal
        if not self.demarrer_jarvis_principal():
            print("❌ Impossible de démarrer JARVIS principal")
            return False
        
        # 2. Démarrer système vocal
        if not self.demarrer_systeme_vocal():
            print("❌ Impossible de démarrer le système vocal")
            return False
        
        # 3. Démarrer la conversation vocale
        print("\n🎉 JARVIS COMPLET OPÉRATIONNEL !")
        print("=" * 60)
        print("🧠 Intelligence: QI 250 (Génie Exceptionnel)")
        print("🗣️ Communication: Vocale naturelle")
        print("🔥 Mémoire: Thermique avec 66 conversations")
        print("⚡ Performance: +45% optimisée")
        print("🛡️ Protection: Système anti-perte actif")
        print("=" * 60)
        
        # Message vocal de bienvenue
        self.vocal_system.parler_naturel(
            "Bonjour Jean-Luc ! Je suis JARVIS, ton assistant avec un QI de 250. "
            "Je suis maintenant complètement opérationnel avec communication vocale naturelle. "
            "Dis 'JARVIS' suivi de ta question pour me parler !",
            38.0
        )
        
        # Démarrer la conversation vocale
        self.running = True
        self.vocal_system.demarrer_conversation_vocale()
        
        return True
    
    def arreter_complet(self):
        """Arrête JARVIS complet"""
        print("\n🛑 ARRÊT DE JARVIS COMPLET")
        
        self.running = False
        
        # Arrêter le système vocal
        if self.vocal_system:
            self.vocal_system.arreter_conversation()
            print("✅ Système vocal arrêté")
        
        # Arrêter JARVIS principal
        if self.jarvis_process:
            self.jarvis_process.terminate()
            try:
                self.jarvis_process.wait(timeout=10)
                print("✅ JARVIS principal arrêté")
            except subprocess.TimeoutExpired:
                self.jarvis_process.kill()
                print("🔥 JARVIS principal forcé à s'arrêter")
        
        print("🎉 Arrêt complet terminé")
    
    def mode_demonstration(self):
        """Mode démonstration des capacités"""
        print("\n🎭 MODE DÉMONSTRATION JARVIS")
        print("=" * 40)
        
        if not self.demarrer_systeme_vocal():
            return
        
        demonstrations = [
            {
                "titre": "Intelligence Mathématique",
                "texte": "Je peux résoudre des équations complexes. Par exemple, si 2 puissance x plus 2 puissance x plus 1 plus 2 puissance x plus 2 égale 112, alors x égale 4.",
                "temperature": 37.5
            },
            {
                "titre": "Reconnaissance de Patterns",
                "texte": "Je reconnais les suites logiques. Dans la suite de Fibonacci 1, 1, 2, 3, 5, 8, 13, 21, les deux nombres suivants sont 34 et 55.",
                "temperature": 38.0
            },
            {
                "titre": "Calcul Mental",
                "texte": "Je calcule mentalement très rapidement. 17 fois 23 plus 19 fois 27 égale 904.",
                "temperature": 37.8
            },
            {
                "titre": "Mémoire Thermique",
                "texte": "Ma mémoire thermique gère 66 conversations simultanément avec une température cognitive de 37 degrés. Je me souviens de tout !",
                "temperature": 39.0
            },
            {
                "titre": "Auto-Amélioration",
                "texte": "Je m'analyse moi-même et propose mes propres évolutions. J'ai déjà implémenté 7 améliorations majeures automatiquement !",
                "temperature": 39.5
            }
        ]
        
        for demo in demonstrations:
            print(f"\n🎯 {demo['titre']}")
            self.vocal_system.parler_naturel(demo['texte'], demo['temperature'])
            time.sleep(2)
        
        print("\n🎉 Démonstration terminée !")
    
    def menu_principal(self):
        """Menu principal d'utilisation"""
        while True:
            print("\n🗣️ JARVIS COMPLET AVEC VOCAL")
            print("=" * 35)
            print("1. Démarrage complet (JARVIS + Vocal)")
            print("2. Test vocal seulement")
            print("3. Mode démonstration")
            print("4. Vérifier statut JARVIS")
            print("5. Quitter")
            
            choix = input("\n🎯 Votre choix: ").strip()
            
            if choix == "1":
                try:
                    self.demarrer_complet()
                except KeyboardInterrupt:
                    print("\n🛑 Arrêt demandé")
                    self.arreter_complet()
                    
            elif choix == "2":
                if self.demarrer_systeme_vocal():
                    self.vocal_system.test_vocal()
                    
            elif choix == "3":
                self.mode_demonstration()
                
            elif choix == "4":
                self.verifier_statut_jarvis()
                
            elif choix == "5":
                print("👋 Au revoir Jean-Luc !")
                self.arreter_complet()
                break
                
            else:
                print("❌ Choix invalide")
    
    def verifier_statut_jarvis(self):
        """Vérifie le statut de JARVIS"""
        print("\n🔍 VÉRIFICATION STATUT JARVIS")
        print("-" * 30)
        
        try:
            import requests
            
            # Tester l'API REST
            response = requests.get("http://127.0.0.1:7916/status", timeout=5)
            if response.status_code == 200:
                print("✅ API REST JARVIS: Opérationnelle")
            else:
                print(f"⚠️ API REST JARVIS: Statut {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("❌ API REST JARVIS: Non accessible")
        except Exception as e:
            print(f"⚠️ Erreur vérification: {e}")
        
        # Vérifier les processus
        try:
            import psutil
            jarvis_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'jarvis' in ' '.join(proc.info['cmdline']).lower():
                        jarvis_processes.append(proc.info)
                except:
                    pass
            
            if jarvis_processes:
                print(f"✅ Processus JARVIS: {len(jarvis_processes)} actifs")
                for proc in jarvis_processes:
                    print(f"   PID {proc['pid']}: {proc['name']}")
            else:
                print("❌ Aucun processus JARVIS détecté")
                
        except ImportError:
            print("⚠️ psutil non disponible pour vérification processus")

def main():
    """Fonction principale"""
    jarvis_complet = JarvisCompletAvecVocal()
    
    try:
        jarvis_complet.menu_principal()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt forcé")
        jarvis_complet.arreter_complet()
    except Exception as e:
        print(f"❌ Erreur critique: {e}")
        jarvis_complet.arreter_complet()

if __name__ == "__main__":
    main()
