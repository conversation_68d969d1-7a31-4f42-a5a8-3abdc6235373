#!/usr/bin/env python3
"""
🔧 INSTALLATEUR SYSTÈME VOCAL JARVIS
Installe toutes les dépendances nécessaires pour la communication vocale
"""

import subprocess
import sys
import platform
import os

def installer_dependances():
    """Installe toutes les dépendances vocales"""
    print("🔧 INSTALLATION SYSTÈME VOCAL JARVIS")
    print("=" * 40)
    
    # Dépendances Python
    dependances_python = [
        "SpeechRecognition",
        "pyttsx3",
        "pyaudio",
        "requests"
    ]
    
    print("📦 Installation des dépendances Python...")
    for dep in dependances_python:
        try:
            print(f"   Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"   ✅ {dep} installé")
        except Exception as e:
            print(f"   ⚠️ Erreur {dep}: {e}")
    
    # Dépendances système selon l'OS
    system = platform.system()
    
    if system == "Darwin":  # macOS
        print("\n🍎 Configuration macOS...")
        print("   ✅ Voix système macOS disponibles")
        print("   ✅ Microphone système disponible")
        
    elif system == "Linux":
        print("\n🐧 Configuration Linux...")
        try:
            subprocess.run(["sudo", "apt-get", "update"], check=True)
            subprocess.run(["sudo", "apt-get", "install", "-y", "espeak", "espeak-data", "libespeak1", "libespeak-dev"], check=True)
            subprocess.run(["sudo", "apt-get", "install", "-y", "portaudio19-dev", "python3-pyaudio"], check=True)
            print("   ✅ Dépendances Linux installées")
        except Exception as e:
            print(f"   ⚠️ Erreur Linux: {e}")
    
    elif system == "Windows":
        print("\n🪟 Configuration Windows...")
        print("   ✅ Voix système Windows disponibles")
        print("   ⚠️ Assurez-vous que le microphone est configuré")
    
    print("\n✅ Installation terminée !")
    print("🗣️ Vous pouvez maintenant utiliser JARVIS vocal")

def tester_installation():
    """Teste l'installation vocale"""
    print("\n🧪 TEST DE L'INSTALLATION")
    print("-" * 30)
    
    try:
        import speech_recognition as sr
        print("✅ SpeechRecognition OK")
    except ImportError:
        print("❌ SpeechRecognition manquant")
        return False
    
    try:
        import pyttsx3
        print("✅ pyttsx3 OK")
    except ImportError:
        print("❌ pyttsx3 manquant")
        return False
    
    try:
        # Test microphone
        r = sr.Recognizer()
        mic = sr.Microphone()
        print("✅ Microphone détecté")
    except Exception as e:
        print(f"⚠️ Problème microphone: {e}")
    
    try:
        # Test TTS
        engine = pyttsx3.init()
        print("✅ Synthèse vocale OK")
        
        # Test voix
        voices = engine.getProperty('voices')
        print(f"✅ {len(voices)} voix disponibles")
        
        for i, voice in enumerate(voices[:3]):  # Afficher les 3 premières
            print(f"   {i+1}. {voice.name}")
            
    except Exception as e:
        print(f"⚠️ Problème TTS: {e}")
    
    print("\n🎉 Test terminé !")
    return True

if __name__ == "__main__":
    installer_dependances()
    tester_installation()
