#!/usr/bin/env python3
"""
🎥 INTERFACE COMPLÈTE JARVIS AVEC CAMÉRA ET AUDIO
Interface web avec microphone, haut-parleur et vision en temps réel
"""

import gradio as gr
import cv2
import numpy as np
import base64
import io
import time
import threading
import queue
from datetime import datetime
import requests
import json

# Ajouter le système vocal
try:
    from jarvis_vocal_naturel import JarvisVocalNaturel
    VOCAL_DISPONIBLE = True
except ImportError:
    VOCAL_DISPONIBLE = False
    print("⚠️ Système vocal non disponible")

class JarvisInterfaceComplete:
    """🎥 Interface complète JARVIS avec audio et vidéo"""
    
    def __init__(self):
        self.camera = None
        self.vocal_system = None
        self.conversation_history = []
        self.camera_active = False
        self.audio_queue = queue.Queue()
        
        # Initialiser la caméra
        self.initialiser_camera()
        
        # Initialiser le système vocal
        if VOCAL_DISPONIBLE:
            self.initialiser_vocal()
        
        print("🎥 INTERFACE COMPLÈTE JARVIS INITIALISÉE")
        print("📹 Caméra: " + ("✅ Active" if self.camera_active else "❌ Indisponible"))
        print("🗣️ Vocal: " + ("✅ Active" if VOCAL_DISPONIBLE else "❌ Indisponible"))
    
    def initialiser_camera(self):
        """Initialise la caméra"""
        try:
            self.camera = cv2.VideoCapture(0)
            if self.camera.isOpened():
                self.camera_active = True
                print("📹 Caméra initialisée")
            else:
                print("⚠️ Caméra non disponible")
        except Exception as e:
            print(f"⚠️ Erreur caméra: {e}")
    
    def initialiser_vocal(self):
        """Initialise le système vocal"""
        try:
            self.vocal_system = JarvisVocalNaturel()
            print("🗣️ Système vocal initialisé")
        except Exception as e:
            print(f"⚠️ Erreur vocal: {e}")
    
    def capturer_image(self):
        """Capture une image de la caméra"""
        if not self.camera_active:
            return None
        
        try:
            ret, frame = self.camera.read()
            if ret:
                # Retourner l'image pour Gradio
                return cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            return None
        except Exception as e:
            print(f"⚠️ Erreur capture: {e}")
            return None
    
    def analyser_image_pour_jarvis(self, image):
        """Analyse l'image pour JARVIS"""
        if image is None:
            return "Aucune image disponible"
        
        # Analyse basique de l'image
        height, width = image.shape[:2]
        couleur_moyenne = np.mean(image, axis=(0, 1))
        
        # Détection de mouvement basique (à améliorer)
        luminosite = np.mean(cv2.cvtColor(image, cv2.COLOR_RGB2GRAY))
        
        description = f"""
🎥 ANALYSE VISUELLE:
📏 Résolution: {width}x{height}
🌈 Couleur moyenne: RGB({couleur_moyenne[0]:.0f}, {couleur_moyenne[1]:.0f}, {couleur_moyenne[2]:.0f})
💡 Luminosité: {luminosite:.0f}/255
⏰ Timestamp: {datetime.now().strftime('%H:%M:%S')}
"""
        return description
    
    def chat_avec_jarvis(self, message, history, image=None):
        """Chat avec JARVIS incluant l'analyse d'image"""
        if not message.strip():
            return history, ""
        
        # Ajouter l'analyse d'image si disponible
        contexte_visuel = ""
        if image is not None:
            contexte_visuel = self.analyser_image_pour_jarvis(image)
        
        # Préparer le message complet
        message_complet = message
        if contexte_visuel:
            message_complet += f"\n\nContexte visuel:\n{contexte_visuel}"
        
        # Envoyer à JARVIS
        try:
            payload = {
                "message": message_complet,
                "agent": "deepseek",
                "mode_vocal": True,
                "contexte_visuel": bool(contexte_visuel)
            }
            
            response = requests.post("http://127.0.0.1:7916/chat", json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                reponse = data.get("response", "Erreur de réponse")
                temperature = data.get("temperature", 37.0)
                
                # Réponse vocale si disponible
                if self.vocal_system:
                    threading.Thread(
                        target=self.vocal_system.parler_naturel,
                        args=(reponse, temperature),
                        daemon=True
                    ).start()
                
            else:
                reponse = f"Erreur API: {response.status_code}"
                
        except Exception as e:
            reponse = f"Erreur communication: {e}"
        
        # Ajouter à l'historique
        history.append([message, reponse])
        
        return history, ""
    
    def commande_vocale(self, audio):
        """Traite une commande vocale"""
        if not VOCAL_DISPONIBLE:
            return "Système vocal non disponible"
        
        # Ici on pourrait traiter l'audio reçu
        # Pour l'instant, message d'information
        return "🎤 Audio reçu - Traitement vocal en cours..."
    
    def creer_interface(self):
        """Crée l'interface Gradio complète"""
        
        with gr.Blocks(
            title="🎥 JARVIS - Interface Complète",
            css="""
            .gradio-container {
                background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            }
            .chat-container {
                background: rgba(0,0,0,0.3);
                border-radius: 15px;
                padding: 20px;
            }
            .camera-container {
                background: rgba(0,0,0,0.2);
                border-radius: 15px;
                padding: 15px;
            }
            """
        ) as interface:
            
            gr.HTML("""
            <div style="text-align: center; padding: 20px;">
                <h1 style="color: #00ff88; font-size: 3em; margin: 0;">
                    🎥 JARVIS - Interface Complète
                </h1>
                <p style="color: #ffffff; font-size: 1.2em; margin: 10px 0;">
                    🧠 QI 250 | 🗣️ Vocal Naturel | 📹 Vision Temps Réel | 🎤 Audio Interactif
                </p>
                <p style="color: #00ff88; font-size: 1em;">
                    Assistant Intelligent avec Communication Multimodale
                </p>
            </div>
            """)
            
            with gr.Row():
                # Colonne gauche - Caméra et contrôles
                with gr.Column(scale=1):
                    gr.HTML("<h2 style='color: #00ff88;'>📹 Vision Temps Réel</h2>")
                    
                    # Flux caméra
                    camera_feed = gr.Image(
                        label="🎥 Caméra",
                        sources=["webcam"],
                        streaming=True,
                        elem_classes=["camera-container"]
                    )
                    
                    # Contrôles audio
                    gr.HTML("<h3 style='color: #00ff88;'>🎤 Audio</h3>")
                    
                    audio_input = gr.Audio(
                        label="🎤 Parlez à JARVIS",
                        sources=["microphone"],
                        type="numpy"
                    )
                    
                    btn_vocal = gr.Button("🗣️ Commande Vocale", variant="primary")
                    
                    # Statut système
                    gr.HTML("<h3 style='color: #00ff88;'>📊 Statut Système</h3>")
                    statut_display = gr.HTML(f"""
                    <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                        <p>📹 Caméra: {'✅ Active' if self.camera_active else '❌ Indisponible'}</p>
                        <p>🗣️ Vocal: {'✅ Active' if VOCAL_DISPONIBLE else '❌ Indisponible'}</p>
                        <p>🧠 JARVIS: ✅ QI 250</p>
                        <p>🔥 Température: 37.0°C</p>
                    </div>
                    """)
                
                # Colonne droite - Chat
                with gr.Column(scale=2):
                    gr.HTML("<h2 style='color: #00ff88;'>💬 Conversation avec JARVIS</h2>")
                    
                    chatbot = gr.Chatbot(
                        label="🤖 JARVIS",
                        height=500,
                        elem_classes=["chat-container"],
                        avatar_images=("👤", "🤖")
                    )
                    
                    with gr.Row():
                        msg_input = gr.Textbox(
                            label="💬 Message",
                            placeholder="Tapez votre message à JARVIS...",
                            scale=4
                        )
                        btn_send = gr.Button("📤 Envoyer", variant="primary", scale=1)
                    
                    # Boutons rapides
                    with gr.Row():
                        btn_salut = gr.Button("👋 Salut JARVIS")
                        btn_heure = gr.Button("⏰ Quelle heure ?")
                        btn_analyse = gr.Button("🔍 Analyse image")
                        btn_clear = gr.Button("🗑️ Effacer", variant="secondary")
            
            # Événements
            def envoyer_message(message, history, image):
                return self.chat_avec_jarvis(message, history, image)
            
            def saluer():
                return [["👋 Salut JARVIS", "Bonjour Jean-Luc ! Je suis JARVIS, votre assistant avec un QI de 250. Comment puis-je vous aider aujourd'hui ?"]]
            
            def demander_heure():
                heure_actuelle = datetime.now().strftime("%H:%M:%S")
                return [["⏰ Quelle heure est-il ?", f"Il est actuellement {heure_actuelle}, Jean-Luc."]]
            
            def analyser_image_actuelle(image):
                if image is not None:
                    analyse = self.analyser_image_pour_jarvis(image)
                    return [["🔍 Analyse cette image", f"Voici mon analyse visuelle :\n{analyse}"]]
                return [["🔍 Analyse cette image", "Aucune image disponible pour l'analyse."]]
            
            # Connexions des événements
            btn_send.click(
                envoyer_message,
                inputs=[msg_input, chatbot, camera_feed],
                outputs=[chatbot, msg_input]
            )
            
            msg_input.submit(
                envoyer_message,
                inputs=[msg_input, chatbot, camera_feed],
                outputs=[chatbot, msg_input]
            )
            
            btn_salut.click(saluer, outputs=[chatbot])
            btn_heure.click(demander_heure, outputs=[chatbot])
            btn_analyse.click(analyser_image_actuelle, inputs=[camera_feed], outputs=[chatbot])
            btn_clear.click(lambda: [], outputs=[chatbot])
            
            btn_vocal.click(
                self.commande_vocale,
                inputs=[audio_input],
                outputs=[msg_input]
            )
        
        return interface
    
    def lancer_interface(self, port=7919):
        """Lance l'interface complète"""
        interface = self.creer_interface()
        
        print(f"\n🚀 LANCEMENT INTERFACE COMPLÈTE JARVIS")
        print(f"🌐 URL: http://localhost:{port}")
        print(f"📹 Caméra: {'✅ Active' if self.camera_active else '❌ Indisponible'}")
        print(f"🗣️ Vocal: {'✅ Active' if VOCAL_DISPONIBLE else '❌ Indisponible'}")
        print(f"🎤 Microphone: ✅ Intégré")
        print(f"🔊 Haut-parleur: ✅ Intégré")
        
        interface.launch(
            server_name="localhost",
            server_port=port,
            share=False,
            debug=False,
            show_error=True
        )
    
    def __del__(self):
        """Nettoyage"""
        if self.camera:
            self.camera.release()

def main():
    """Fonction principale"""
    jarvis_interface = JarvisInterfaceComplete()
    jarvis_interface.lancer_interface()

if __name__ == "__main__":
    main()
