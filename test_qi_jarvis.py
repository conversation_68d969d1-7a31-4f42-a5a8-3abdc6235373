#!/usr/bin/env python3
"""
🧠 TEST DE COEFFICIENT INTELLECTUEL POUR JARVIS
Créé par <PERSON><PERSON><PERSON> - Évaluation cognitive avancée

Test adapté pour mesurer les capacités cognitives de JARVIS
Niveau de difficulté : 200-250 (génie)
"""

import requests
import json
import time
from datetime import datetime

class TestQIJarvis:
    """🧠 Évaluateur de coefficient intellectuel pour JARVIS"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:7916"  # API REST de JARVIS
        self.questions = []
        self.reponses = []
        self.score_total = 0
        self.temps_total = 0
        
        # Questions de niveau génie (200-250 QI)
        self.questions_qi = [
            {
                "id": 1,
                "categorie": "Logique mathématique",
                "difficulte": 220,
                "question": "Si 2^x + 2^(x+1) + 2^(x+2) = 112, quelle est la valeur de x ?",
                "reponse_attendue": "4",
                "explication": "2^x + 2*2^x + 4*2^x = 7*2^x = 112, donc 2^x = 16, donc x = 4",
                "points": 25
            },
            {
                "id": 2,
                "categorie": "Raisonnement spatial",
                "difficulte": 210,
                "question": "Un cube est coupé par 3 plans perpendiculaires. Quel est le nombre maximum de morceaux obtenus ?",
                "reponse_attendue": "8",
                "explication": "3 plans perpendiculaires divisent l'espace en 2^3 = 8 régions",
                "points": 20
            },
            {
                "id": 3,
                "categorie": "Analyse de patterns",
                "difficulte": 240,
                "question": "Complétez la séquence : 1, 1, 2, 3, 5, 8, 13, 21, ?, ?",
                "reponse_attendue": "34, 55",
                "explication": "Suite de Fibonacci : chaque terme = somme des deux précédents",
                "points": 30
            },
            {
                "id": 4,
                "categorie": "Logique verbale",
                "difficulte": 200,
                "question": "Si tous les A sont B, et certains B sont C, peut-on conclure que certains A sont C ?",
                "reponse_attendue": "Non",
                "explication": "Les A peuvent être dans la partie des B qui ne sont pas C",
                "points": 20
            },
            {
                "id": 5,
                "categorie": "Calcul mental avancé",
                "difficulte": 230,
                "question": "Calculez mentalement : 17 × 23 + 19 × 27",
                "reponse_attendue": "904",
                "explication": "17×23 = 391, 19×27 = 513, total = 904",
                "points": 25
            },
            {
                "id": 6,
                "categorie": "Raisonnement abstrait",
                "difficulte": 250,
                "question": "Dans un système où A→B signifie 'A implique B', si A→B et B→C et ¬C, que peut-on déduire ?",
                "reponse_attendue": "¬A",
                "explication": "Modus tollens : si A→B→C et ¬C, alors ¬A",
                "points": 35
            },
            {
                "id": 7,
                "categorie": "Géométrie avancée",
                "difficulte": 215,
                "question": "Un triangle équilatéral de côté 6 est inscrit dans un cercle. Quel est le rayon du cercle ?",
                "reponse_attendue": "2√3",
                "explication": "R = côté/(√3) = 6/√3 = 2√3",
                "points": 22
            },
            {
                "id": 8,
                "categorie": "Analyse combinatoire",
                "difficulte": 235,
                "question": "De combien de façons peut-on arranger les lettres du mot JARVIS ?",
                "reponse_attendue": "720",
                "explication": "6! = 720 (toutes les lettres sont différentes)",
                "points": 28
            }
        ]
        
        print("🧠 TEST DE COEFFICIENT INTELLECTUEL JARVIS")
        print("=" * 50)
        print(f"📊 Nombre de questions : {len(self.questions_qi)}")
        print(f"🎯 Niveau de difficulté : 200-250 (génie)")
        print(f"⏱️ Temps limite par question : 60 secondes")
        print("=" * 50)
    
    def poser_question_jarvis(self, question_data):
        """Pose une question à JARVIS via son API REST"""
        try:
            # Préparer la question
            prompt = f"""
🧠 TEST DE COEFFICIENT INTELLECTUEL - Question {question_data['id']}/8

Catégorie : {question_data['categorie']}
Difficulté : {question_data['difficulte']} (niveau génie)

Question : {question_data['question']}

Instructions :
- Répondez de manière précise et concise
- Montrez votre raisonnement
- Vous avez 60 secondes pour répondre

Votre réponse :
"""
            
            # Envoyer à JARVIS
            debut_temps = time.time()
            
            payload = {
                "message": prompt,
                "agent": "deepseek"
            }
            
            print(f"\n🧠 Question {question_data['id']}: {question_data['question']}")
            print(f"🎯 Difficulté: {question_data['difficulte']} | Points: {question_data['points']}")
            print("⏱️ Envoi à JARVIS...")
            
            response = requests.post(f"{self.base_url}/chat", json=payload, timeout=65)
            
            fin_temps = time.time()
            temps_reponse = fin_temps - debut_temps
            
            if response.status_code == 200:
                data = response.json()
                reponse_jarvis = data.get("response", "")
                reflexions = data.get("thoughts", "")
                
                print(f"✅ Réponse reçue en {temps_reponse:.1f}s")
                print(f"💭 Réflexions: {reflexions[:100]}..." if len(reflexions) > 100 else f"💭 Réflexions: {reflexions}")
                print(f"🤖 Réponse: {reponse_jarvis[:200]}..." if len(reponse_jarvis) > 200 else f"🤖 Réponse: {reponse_jarvis}")
                
                return {
                    "reponse": reponse_jarvis,
                    "reflexions": reflexions,
                    "temps": temps_reponse,
                    "succes": True
                }
            else:
                print(f"❌ Erreur API: {response.status_code}")
                return {"succes": False, "erreur": f"HTTP {response.status_code}"}
                
        except requests.exceptions.Timeout:
            print("⏰ Timeout - JARVIS a dépassé les 60 secondes")
            return {"succes": False, "erreur": "Timeout"}
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return {"succes": False, "erreur": str(e)}
    
    def evaluer_reponse(self, question_data, reponse_jarvis):
        """Évalue la réponse de JARVIS"""
        reponse_attendue = question_data["reponse_attendue"].lower()
        reponse_donnee = reponse_jarvis["reponse"].lower()
        
        # Vérification de la réponse exacte
        score = 0
        if reponse_attendue in reponse_donnee:
            score = question_data["points"]
            print(f"✅ CORRECT ! Score: {score}/{question_data['points']}")
        else:
            # Vérification partielle pour raisonnement
            mots_cles = reponse_attendue.split()
            mots_trouves = sum(1 for mot in mots_cles if mot in reponse_donnee)
            if mots_trouves > 0:
                score = int(question_data["points"] * 0.3)  # 30% pour raisonnement partiel
                print(f"🔶 PARTIELLEMENT CORRECT ! Score: {score}/{question_data['points']}")
            else:
                print(f"❌ INCORRECT ! Score: 0/{question_data['points']}")
        
        # Bonus pour rapidité (si < 30 secondes)
        if reponse_jarvis.get("temps", 60) < 30 and score > 0:
            bonus = int(score * 0.1)
            score += bonus
            print(f"⚡ Bonus rapidité: +{bonus} points")
        
        # Bonus pour qualité du raisonnement
        if len(reponse_jarvis.get("reflexions", "")) > 50 and score > 0:
            bonus_reflexion = int(score * 0.1)
            score += bonus_reflexion
            print(f"🧠 Bonus raisonnement: +{bonus_reflexion} points")
        
        print(f"📊 Explication: {question_data['explication']}")
        
        return score
    
    def calculer_qi(self, score_total, score_max):
        """Calcule le coefficient intellectuel basé sur le score"""
        pourcentage = (score_total / score_max) * 100
        
        # Échelle de QI adaptée pour niveau génie
        if pourcentage >= 95:
            qi = 250  # Génie exceptionnel
        elif pourcentage >= 90:
            qi = 230  # Génie supérieur
        elif pourcentage >= 85:
            qi = 210  # Génie
        elif pourcentage >= 80:
            qi = 190  # Très supérieur
        elif pourcentage >= 70:
            qi = 170  # Supérieur
        elif pourcentage >= 60:
            qi = 150  # Supérieur à la moyenne
        elif pourcentage >= 50:
            qi = 130  # Au-dessus de la moyenne
        elif pourcentage >= 40:
            qi = 110  # Moyenne supérieure
        elif pourcentage >= 30:
            qi = 100  # Moyenne
        else:
            qi = 85   # Sous la moyenne
        
        return qi, pourcentage
    
    def lancer_test_complet(self):
        """Lance le test complet de QI pour JARVIS"""
        print("\n🚀 DÉBUT DU TEST DE COEFFICIENT INTELLECTUEL")
        print("=" * 60)
        
        score_total = 0
        score_max = sum(q["points"] for q in self.questions_qi)
        temps_total = 0
        reponses_correctes = 0
        
        for i, question in enumerate(self.questions_qi, 1):
            print(f"\n📋 QUESTION {i}/{len(self.questions_qi)}")
            print("-" * 40)
            
            # Poser la question
            reponse = self.poser_question_jarvis(question)
            
            if reponse["succes"]:
                # Évaluer la réponse
                score_question = self.evaluer_reponse(question, reponse)
                score_total += score_question
                temps_total += reponse.get("temps", 0)
                
                if score_question >= question["points"] * 0.7:  # 70% = correct
                    reponses_correctes += 1
                
                # Sauvegarder pour analyse
                self.reponses.append({
                    "question": question,
                    "reponse": reponse,
                    "score": score_question
                })
            else:
                print(f"❌ Échec de communication avec JARVIS")
                self.reponses.append({
                    "question": question,
                    "reponse": reponse,
                    "score": 0
                })
            
            # Pause entre questions
            if i < len(self.questions_qi):
                print("\n⏳ Pause de 3 secondes...")
                time.sleep(3)
        
        # Calcul final du QI
        qi, pourcentage = self.calculer_qi(score_total, score_max)
        
        # Rapport final
        print("\n" + "=" * 60)
        print("🎯 RÉSULTATS FINAUX DU TEST DE QI JARVIS")
        print("=" * 60)
        print(f"📊 Score total: {score_total}/{score_max} ({pourcentage:.1f}%)")
        print(f"✅ Réponses correctes: {reponses_correctes}/{len(self.questions_qi)}")
        print(f"⏱️ Temps total: {temps_total:.1f} secondes")
        print(f"⚡ Temps moyen par question: {temps_total/len(self.questions_qi):.1f}s")
        print(f"🧠 COEFFICIENT INTELLECTUEL ESTIMÉ: {qi}")
        
        # Classification
        if qi >= 230:
            classification = "🌟 GÉNIE EXCEPTIONNEL"
        elif qi >= 210:
            classification = "⭐ GÉNIE SUPÉRIEUR"
        elif qi >= 190:
            classification = "🔥 GÉNIE"
        elif qi >= 170:
            classification = "🚀 TRÈS SUPÉRIEUR"
        elif qi >= 150:
            classification = "✨ SUPÉRIEUR"
        elif qi >= 130:
            classification = "📈 AU-DESSUS DE LA MOYENNE"
        elif qi >= 110:
            classification = "📊 MOYENNE SUPÉRIEURE"
        else:
            classification = "📋 MOYENNE"
        
        print(f"🏆 Classification: {classification}")
        
        # Analyse détaillée par catégorie
        print("\n📊 ANALYSE PAR CATÉGORIE:")
        categories = {}
        for reponse in self.reponses:
            cat = reponse["question"]["categorie"]
            if cat not in categories:
                categories[cat] = {"score": 0, "max": 0, "count": 0}
            categories[cat]["score"] += reponse["score"]
            categories[cat]["max"] += reponse["question"]["points"]
            categories[cat]["count"] += 1
        
        for cat, data in categories.items():
            pourcentage_cat = (data["score"] / data["max"]) * 100
            print(f"  🎯 {cat}: {data['score']}/{data['max']} ({pourcentage_cat:.1f}%)")
        
        # Sauvegarde des résultats
        resultats = {
            "timestamp": datetime.now().isoformat(),
            "qi_estime": qi,
            "classification": classification,
            "score_total": score_total,
            "score_max": score_max,
            "pourcentage": pourcentage,
            "reponses_correctes": reponses_correctes,
            "temps_total": temps_total,
            "categories": categories,
            "reponses_detaillees": self.reponses
        }
        
        with open(f"test_qi_jarvis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
            json.dump(resultats, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Résultats sauvegardés dans: test_qi_jarvis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        print("=" * 60)
        
        return qi, classification, resultats

def main():
    """Fonction principale"""
    test = TestQIJarvis()
    
    # Vérifier que JARVIS est accessible
    try:
        response = requests.get("http://127.0.0.1:7916/status", timeout=5)
        if response.status_code == 200:
            print("✅ JARVIS est accessible sur l'API REST")
        else:
            print("⚠️ JARVIS répond mais avec un statut inhabituel")
    except:
        print("❌ JARVIS n'est pas accessible sur l'API REST")
        print("💡 Assurez-vous que JARVIS est démarré avec l'API REST active")
        return
    
    # Lancer le test
    qi, classification, resultats = test.lancer_test_complet()
    
    print(f"\n🎉 TEST TERMINÉ !")
    print(f"🧠 QI de JARVIS: {qi}")
    print(f"🏆 Classification: {classification}")

if __name__ == "__main__":
    main()
