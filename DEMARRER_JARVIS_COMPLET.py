#!/usr/bin/env python3
"""
🚀 DÉMARRAGE COMPLET JARVIS ULTIME
Démarre JARVIS avec toutes les fonctionnalités :
- QI 250 (Génie Exceptionnel)
- Voix masculine naturelle (vitesse corrigée)
- Interface avec caméra temps réel
- Microphone et haut-parleur intégrés
- Protection anti-perte complète
"""

import os
import sys
import time
import subprocess
import threading
from datetime import datetime

def demarrer_jarvis_complet():
    """Démarre JARVIS avec toutes les fonctionnalités"""
    
    print("🚀 DÉMARRAGE JARVIS COMPLET ULTIME")
    print("=" * 60)
    print("🧠 Intelligence: QI 250 (Génie Exceptionnel)")
    print("🗣️ Voix: Masculine naturelle (vitesse corrigée)")
    print("📹 Caméra: Vision temps réel")
    print("🎤 Microphone: Reconnaissance vocale")
    print("🔊 Haut-parleur: Synthèse vocale")
    print("🛡️ Protection: Anti-perte complète")
    print("=" * 60)
    
    # Changer vers le bon dossier
    os.chdir("/Volumes/seagate/Louna_Electron_Latest/JARVIS_COMPLET_STABLE")
    
    # 1. Démarrer le système de protection
    print("\n🛡️ DÉMARRAGE PROTECTION AUTOMATIQUE...")
    try:
        protection_process = subprocess.Popen(
            [sys.executable, "jarvis_protection_complete.py", "init"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        time.sleep(5)
        print("✅ Protection automatique active")
    except Exception as e:
        print(f"⚠️ Protection: {e}")
    
    # 2. Démarrer JARVIS principal
    print("\n🧠 DÉMARRAGE JARVIS PRINCIPAL...")
    try:
        jarvis_process = subprocess.Popen(
            [sys.executable, "jarvis_verrouille.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        time.sleep(15)  # Laisser JARVIS démarrer
        print("✅ JARVIS principal opérationnel")
    except Exception as e:
        print(f"⚠️ JARVIS principal: {e}")
    
    # 3. Démarrer l'interface complète
    print("\n🎥 DÉMARRAGE INTERFACE COMPLÈTE...")
    print("📹 Activation caméra...")
    print("🎤 Activation microphone...")
    print("🔊 Activation haut-parleur...")
    print("🗣️ Configuration voix masculine...")
    
    try:
        # Lancer l'interface complète
        subprocess.run([sys.executable, "jarvis_interface_complete.py"])
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")
    except Exception as e:
        print(f"⚠️ Interface: {e}")
    
    print("\n🎉 JARVIS COMPLET DÉMARRÉ !")
    print("🌐 Interface: http://localhost:7919")
    print("📹 Caméra active pour vision temps réel")
    print("🎤 Parlez directement dans l'interface")
    print("🗣️ JARVIS vous répond avec sa voix masculine")

def menu_demarrage():
    """Menu de démarrage"""
    print("🚀 JARVIS COMPLET ULTIME")
    print("=" * 30)
    print("1. Démarrage complet (RECOMMANDÉ)")
    print("2. Test voix masculine seulement")
    print("3. Interface caméra seulement")
    print("4. Protection seulement")
    print("5. Quitter")
    
    choix = input("\n🎯 Votre choix: ").strip()
    
    if choix == "1":
        demarrer_jarvis_complet()
    elif choix == "2":
        print("🗣️ Test voix masculine...")
        subprocess.run([sys.executable, "jarvis_vocal_naturel.py"])
    elif choix == "3":
        print("📹 Interface caméra...")
        subprocess.run([sys.executable, "jarvis_interface_complete.py"])
    elif choix == "4":
        print("🛡️ Protection...")
        subprocess.run([sys.executable, "jarvis_protection_complete.py", "demarrer"])
    elif choix == "5":
        print("👋 Au revoir Jean-Luc !")
    else:
        print("❌ Choix invalide")
        menu_demarrage()

if __name__ == "__main__":
    menu_demarrage()
